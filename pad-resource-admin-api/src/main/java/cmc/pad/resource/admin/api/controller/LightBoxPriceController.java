package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.LightBoxPriceModel;
import cmc.pad.resource.admin.service.dto.LightBoxPriceDto;
import cmc.pad.resource.admin.service.iface.LightBoxPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 灯箱报价
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("light-box/price")
public class LightBoxPriceController {

    private final LightBoxPriceService lightBoxPriceService;

    @Autowired
    LightBoxPriceController(LightBoxPriceService lightBoxPriceService) {
        this.lightBoxPriceService = lightBoxPriceService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated LightBoxPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated LightBoxPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(LightBoxPriceModel.QueryParams params) {
        log.info(">>>查询灯箱刊例价, {}", params);

        // 构建RPC请求参数
        LightBoxPriceDto.QueryPricesRequest request = new LightBoxPriceDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setCityLevel(params.getCityLevel());
        request.setCinemaLevel(params.getCinemaLevel());

        // 调用RPC服务
        LightBoxPriceDto.QueryPricesResponse response = lightBoxPriceService.queryPrices(request);
        List resultList;
        // 转换响应结果
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            resultList = response.getPriceInfos().stream().map(priceInfo -> {
                LightBoxPriceModel.Info info = new LightBoxPriceModel.Info();
                info.setCityLevel(priceInfo.getCityLevel());
                info.setCinemaLevel(priceInfo.getCinemaLevel());
                info.setBasePrice(priceInfo.getBasePrice());
                info.setBaseArea(priceInfo.getBaseArea());
                info.setExtendedPrice(priceInfo.getExtendedPrice());
                return info;
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        log.info(">>>响应:查询灯箱刊例价, request:{} result:{}", request, resultList);
        return resultList;
    }

}
