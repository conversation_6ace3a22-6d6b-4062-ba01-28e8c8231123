package cmc.pad.resource.admin.api.controller;

import org.junit.Assert;
import org.junit.Test;

/**
 * 固定点位租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
public class FixedPointLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testParamCinemaInnerCodeIsNullButCityLevelAndCinemaLevelIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi(""), 2006, "影城级别不存在");
        assertResponseStatusAndMsg(getApi("city_level=1"), 101, "city_level城市级别错误");
        assertResponseStatusAndMsg(getApi("city_level=L1"), 2006, "影城级别不存在");
        assertResponseStatusAndMsg(getApi("cinema_level=a"), 2006, "影城级别不存在");
    }

    @Test
    public void testParamCinemaInnerCodeIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi("cinema_code=849"), 0, "OK");
        assertResponseStatusAndMsg(getApi("cinema_code=304"), 0, "OK");
    }

    @Test
    public void testValidCityLevelAndCinemaLevel() throws Exception {
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":100.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L3&cinema_level=C"), 0, "OK", "");
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=S"), 0, "OK", "");
    }

    @Test
    public void testInvalidCityLevel() throws Exception {
        assertResponseStatusAndMsg(getApi("city_level=L99&cinema_level=A"), 2006, "影城级别不存在");
    }

    @Test
    public void testCinemaCodeOverridesOtherParams() throws Exception {
        // 当提供cinema_code时，city_level和cinema_level参数应该被忽略
        assertResponseStatusAndMsg(getApi("cinema_code=304&cinema_level=INVALID"), 101, "cinema_level影城级别错误");
    }

    @Test
    public void testInvalidCinemaCode() throws Exception {
        assertResponseStatusAndMsg(getApi("cinema_code=99999"), 2007, "影城没有对应的影城级别");
        assertResponseStatusAndMsg(getApi("cinema_code=INVALID"), 101, "cinema_code影城编码错误");
    }

    @Test
    public void testEmptyResponse() throws Exception {
        // 测试返回空列表的情况
        String response = getApi("city_level=L1&cinema_level=A");
        Assert.assertTrue("Response should contain data field", response.contains("\"data\":"));
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/fixed-point-leasing/price/query?" + param;
        System.out.println(path);
        return httpGet(path);
    }
}