package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 营销点位租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/05
 * @Version 1.0
 */
public class MarketingPointLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testParamCinemaInnerCodeIsNullButCityLevelAndCinemaLevelIsNotNull() throws Exception {
        // 测试空参数
        assertResponseStatusAndMsg(getApi(""), 2006, "影城级别不存在");

        // 测试无效的城市级别格式
        assertResponseStatusAndMsg(getApi("city_level=1"), 101, "city_level城市级别错误");

        // 测试城市级别存在但影城级别不存在
        assertResponseStatusAndMsg(getApi("city_level=L1"), 2006, "影城级别不存在");

        // 测试无效的影城级别格式
        assertResponseStatusAndMsg(getApi("cinema_level=a"), 2006, "影城级别不存在");

        // 测试无效的租赁方式
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=INVALID"), 4006, "租赁方式编码不存在");
    }

    @Test
    public void testParamCinemaInnerCodeIsNotNull() throws Exception {
        // 测试有效的影城编码
        assertResponseStatusAndMsg(getApi("cinema_code=849"), 0, "OK", "[]");
        assertResponseStatusAndMsg(getApi("cinema_code=304"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");

        // 测试无效的影城编码格式
        assertResponseStatusAndMsg(getApi("cinema_code=INVALID"), 101, "cinema_code影城编码错误");

        // 测试不存在的影城编码
        assertResponseStatusAndMsg(getApi("cinema_code=99999"), 2007, "影城没有对应的影城级别");
    }

    @Test
    public void testValidCityLevelAndCinemaLevel() throws Exception {
        // 测试无效的城市级别
        assertResponseStatusAndMsg(getApi("city_level=L99&cinema_level=A"), 2006, "影城级别不存在");

        // 测试无效的影城级别
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=Z"), 2006, "影城级别不存在");
    }

    @Test
    public void testLeaseMethodArea() throws Exception {
        // 测试按面积租赁
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=AREA"), 0, "OK");

        // 验证返回的租赁方式为AREA
        String response = getApi("city_level=L1&cinema_level=A&lease_method=AREA");
        if (response.contains("\"data\":") && !response.contains("\"data\":[]")) {
            // 如果有数据，验证租赁方式
            assert response.contains("\"lease_method\":\"AREA\"") : "Response should contain lease_method AREA";
        }
    }

    @Test
    public void testLeaseMethodQuantity() throws Exception {
        // 测试按数量租赁
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=QUANTITY"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
    }


    @Test
    public void testCinemaCodeOverridesOtherParams() throws Exception {
        // 当提供cinema_code时，其他参数应该被忽略或者验证失败
        // 这里测试cinema_code与无效的其他参数组合
        assertResponseStatusAndMsg(getApi("cinema_code=304&cinema_level=INVALID"), 101, "cinema_level影城级别错误");
        assertResponseStatusAndMsg(getApi("cinema_code=304&city_level=INVALID"), 101, "city_level城市级别错误");
        assertResponseStatusAndMsg(getApi("cinema_code=304&lease_method=1"), 101, "lease_method城市级别错误");
    }

    @Test
    public void testInvalidCinemaCode() throws Exception {
        // 测试无效的影城编码
        assertResponseStatusAndMsg(getApi("cinema_code=99999"), 2007, "影城没有对应的影城级别");
        assertResponseStatusAndMsg(getApi("cinema_code=INVALID"), 101, "cinema_code影城编码错误");
        assertResponseStatusAndMsg(getApi("cinema_code=1234567"), 101, "cinema_code影城编码错误"); // 超过6位
    }

    @Test
    public void testLeaseMethodNotFound() throws Exception {
        // 测试返回空列表的情况
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A"), 4006, "租赁方式编码不存在");
    }

    @Test
    public void testParameterCombinations() throws Exception {
        // 测试各种参数组合
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=AREA"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L2&cinema_level=B&lease_method=QUANTITY"), 2006, "影城级别不存在");

        // 测试cinema_code与lease_method组合
        assertResponseStatusAndMsg(getApi("cinema_code=304&lease_method=AREA"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        assertResponseStatusAndMsg(getApi("cinema_code=304&lease_method=QUANTITY"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
    }

    @Test
    public void testCaseInsensitiveLeaseMethod() throws Exception {
        // 测试租赁方式的大小写不敏感（根据LeaseMethod.valueByCode实现）
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=area"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=quantity"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=Area"), 0, "OK","[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A&lease_method=QUANTITY"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/marketing-point-leasing/price/query?" + param;
        System.out.println(path);
        return httpGet(path);
    }
}
