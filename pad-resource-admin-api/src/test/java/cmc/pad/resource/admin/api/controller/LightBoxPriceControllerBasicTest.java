package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 灯箱价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/04
 * @Version 1.0
 */
public class LightBoxPriceControllerBasicTest extends BaseControllerTest {

    @Test
    public void testParamCinemaInnerCodeIsNullButCityLevelAndCinemaLevelIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi("city_level=1"), 101, "city_level城市级别错误");
        assertResponseStatusAndMsg(getApi("cinema_level=a"), 2006, "影城级别不存在");
    }

    @Test
    public void testParamCinemaInnerCodeIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi("cinema_code=849"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"B\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        assertResponseStatusAndMsg(getApi("cinema_code=849&city_level=L1&cinema_level=A"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"B\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        assertResponseStatusAndMsg(getApi("cinema_code=304"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
    }

    @Test
    public void testValidCityLevelAndCinemaLevel() throws Exception {
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=A"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L3&cinema_level=C"), 0, "OK", "[{\"city_level\":\"L3\",\"cinema_level\":\"C\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        assertResponseStatusAndMsg(getApi("city_level=L1&cinema_level=S"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"S\",\"base_price\":88.45,\"base_area\":10,\"extended_price\":65.56}]");
    }

    @Test
    public void testInvalidCityLevel() throws Exception {
        assertResponseStatusAndMsg(getApi("city_level=L99&cinema_level=A"), 2006, "影城级别不存在");
    }

    @Test
    public void testCinemaCodeOverridesOtherParams() throws Exception {
        // 当提供cinema_code时，city_level和cinema_level参数应该被忽略
        assertResponseStatusAndMsg(getApi("cinema_code=304&cinema_level=INVALID"), 101, "cinema_level影城级别错误");
    }

    @Test
    public void testInvalidCinemaCode() throws Exception {
        assertResponseStatusAndMsg(getApi("cinema_code=99999"), 2007, "影城没有对应的影城级别");
        assertResponseStatusAndMsg(getApi("cinema_code=INVALID"), 101, "cinema_code影城编码错误");
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/light-box/price/query?" + param;
        System.out.println(path);
        return httpGet(path);
    }
}
